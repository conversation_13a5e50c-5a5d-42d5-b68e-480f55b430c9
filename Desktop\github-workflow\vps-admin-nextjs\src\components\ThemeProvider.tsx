/**
 * Theme Provider Component
 * Ensures proper theme initialization without hydration mismatches
 */

'use client';

import { useTheme } from '@/hooks/useTheme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export default function ThemeProvider({ children }: ThemeProviderProps) {
  // Initialize theme without conditional rendering to prevent hydration mismatch
  const { theme } = useTheme();

  // Always render children immediately - theme is handled by CSS and layout.tsx script
  return <>{children}</>;
}
