/**
 * ProgressIndicator component for displaying task progress
 */

import React, { useState, useEffect } from 'react';
import { Play, Pause, CheckCircle, XCircle, Clock } from 'lucide-react';
import { ProgressIndicatorProps } from '@/types';

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  task,
  className = ''
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);
  const getStatusIcon = () => {
    switch (task.status) {
      case 'running':
        return <Play size={14} className="text-accent-primary" />;
      case 'paused':
        return <Pause size={14} className="text-accent-warning" />;
      case 'completed':
        return <CheckCircle size={14} className="text-accent-secondary" />;
      case 'failed':
        return <XCircle size={14} className="text-accent-error" />;
      default:
        return <Clock size={14} className="text-theme-tertiary" />;
    }
  };

  const getStatusColor = () => {
    switch (task.status) {
      case 'running':
        return 'bg-accent-primary';
      case 'paused':
        return 'bg-accent-warning';
      case 'completed':
        return 'bg-accent-secondary';
      case 'failed':
        return 'bg-accent-error';
      default:
        return 'bg-theme-tertiary';
    }
  };

  const getStatusText = () => {
    switch (task.status) {
      case 'running':
        return 'In Progress';
      case 'paused':
        return 'Paused';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  };

  const formatDuration = () => {
    if (!mounted || !task.startTime) return null;

    const endTime = task.endTime || new Date();
    const duration = Math.floor((endTime.getTime() - task.startTime.getTime()) / 1000);

    if (duration < 60) {
      return `${duration}s`;
    } else if (duration < 3600) {
      const minutes = Math.floor(duration / 60);
      const seconds = duration % 60;
      return `${minutes}m ${seconds}s`;
    } else {
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  return (
    <div className={`bg-surface-primary border border-theme-primary rounded-lg p-2 lg:p-3 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2 gap-2">
        <div className="flex items-center gap-1 lg:gap-2 min-w-0 flex-1">
          {getStatusIcon()}
          <span className="text-xs lg:text-sm font-medium text-theme-primary truncate">
            {task.title}
          </span>
        </div>
        <div className={`px-1.5 lg:px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
          task.status === 'running' ? 'bg-accent-primary/10 text-accent-primary' :
          task.status === 'completed' ? 'bg-accent-secondary/10 text-accent-secondary' :
          task.status === 'failed' ? 'bg-accent-error/10 text-accent-error' :
          task.status === 'paused' ? 'bg-accent-warning/10 text-accent-warning' :
          'bg-theme-tertiary text-theme-secondary'
        }`}>
          <span className="hidden sm:inline">{getStatusText()}</span>
          <span className="sm:hidden">
            {task.status === 'running' ? '▶' :
             task.status === 'completed' ? '✓' :
             task.status === 'failed' ? '✗' :
             task.status === 'paused' ? '⏸' : '○'}
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-2">
        <div className="flex items-center justify-between text-xs text-theme-secondary mb-1">
          <span>Progress</span>
          <span>{Math.round(task.progress)}%</span>
        </div>
        <div className="w-full bg-theme-tertiary rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}
            style={{ width: `${task.progress}%` }}
          />
        </div>
      </div>

      {/* Task Details */}
      <div className="text-xs text-theme-secondary space-y-1">
        {task.description && (
          <div className="truncate" title={task.description}>
            {task.description}
          </div>
        )}

        <div className="flex items-center justify-between">
          <span>Commands: {task.totalCommands}</span>
          {formatDuration() && (
            <span>Duration: {formatDuration()}</span>
          )}
        </div>

        {task.totalCommands > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-accent-secondary">
              ✓ {task.successfulCommands} successful
            </span>
            {task.failedCommands > 0 && (
              <span className="text-accent-error">
                ✗ {task.failedCommands} failed
              </span>
            )}
          </div>
        )}

        {task.steps.length > 0 && (
          <div className="mt-2">
            <div className="text-gray-500 mb-1">Steps:</div>
            <div className="space-y-1 max-h-20 overflow-y-auto">
              {task.steps.slice(-3).map((step) => (
                <div key={step.id} className="flex items-center gap-2 text-xs">
                  <div className={`w-1.5 h-1.5 rounded-full ${
                    step.status === 'completed' ? 'bg-green-500' :
                    step.status === 'running' ? 'bg-blue-500' :
                    step.status === 'failed' ? 'bg-red-500' :
                    'bg-gray-300'
                  }`} />
                  <span className="truncate">{step.description}</span>
                </div>
              ))}
              {task.steps.length > 3 && (
                <div className="text-xs text-gray-400 text-center">
                  ... and {task.steps.length - 3} more steps
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressIndicator;
